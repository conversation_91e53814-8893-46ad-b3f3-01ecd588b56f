{"name": "vue3-hmap", "version": "0.1.0", "description": "基于华为地图封装的Vue3地图组件库", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:lib": "vite build --mode lib", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "keywords": ["vue3", "华为地图", "huawei-map", "vue-component", "map"], "author": "your-name", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-username/vue3-hmap.git"}, "peerDependencies": {"vue": "^3.3.0"}, "devDependencies": {"@types/node": "^20.5.0", "@vitejs/plugin-vue": "^4.3.1", "typescript": "^5.1.6", "vite": "^4.4.9", "vue": "^3.3.4", "vue-tsc": "^1.8.8"}, "dependencies": {"@vue/shared": "^3.3.4"}}