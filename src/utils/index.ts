import type { HWMapJsSDK } from '@/types'

/**
 * 获取华为地图 SDK 实例
 */
export function getHWMapSDK(): HWMapJsSDK | null {
  if (typeof window !== 'undefined' && window.HWMapJsSDK) {
    return window.HWMapJsSDK
  }
  return null
}

/**
 * 检查华为地图 SDK 是否已加载
 */
export function isHWMapSDKLoaded(): boolean {
  return !!getHWMapSDK()
}

/**
 * 加载华为地图 SDK
 */
export function loadHWMapSDK(apiKey: string, callback?: () => void): Promise<void> {
  return new Promise((resolve, reject) => {
    if (isHWMapSDKLoaded()) {
      resolve()
      callback?.()
      return
    }

    const script = document.createElement('script')
    script.src = `https://mapapi.cloud.huawei.com/mapjs/v1/api/js?callback=initMap&key=${apiKey}`
    script.async = true
    script.defer = true

    // 设置超时时间（10秒）
    const timeout = setTimeout(() => {
      // 清理资源
      if (script.parentNode) {
        script.parentNode.removeChild(script)
      }
      if ((window as any).initMap) {
        delete (window as any).initMap
      }
      reject(new Error('华为地图 SDK 加载超时。请检查网络连接和API Key是否正确'))
    }, 10000)

    // 创建全局回调函数 - 函数名必须与URL中的callback参数一致
    ;(window as any).initMap = () => {
      clearTimeout(timeout)
      resolve()
      callback?.()
      // 清理全局回调函数
      delete (window as any).initMap
    }

    script.onerror = () => {
      clearTimeout(timeout)
      // 清理资源
      if ((window as any).initMap) {
        delete (window as any).initMap
      }
      reject(new Error('华为地图 SDK 网络加载失败。请检查网络连接'))
    }

    document.head.appendChild(script)
  })
}

/**
 * 创建唯一 ID
 */
export function createUniqueId(prefix = 'hmap'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 深度合并对象
 */
export function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target }
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key]
      const targetValue = result[key]
      
      if (isObject(sourceValue) && isObject(targetValue)) {
        result[key] = deepMerge(targetValue, sourceValue as any)
      } else {
        result[key] = sourceValue as any
      }
    }
  }
  
  return result
}

/**
 * 判断是否为对象
 */
function isObject(obj: any): obj is Record<string, any> {
  return obj !== null && typeof obj === 'object' && !Array.isArray(obj)
}

/**
 * 防抖函数
 */
export function debounce<T extends (...args: any[]) => void>(
  func: T,
  wait: number,
  immediate?: boolean
): T {
  let timeout: ReturnType<typeof setTimeout> | null = null
  
  return ((...args: Parameters<T>) => {
    const later = () => {
      timeout = null
      if (!immediate) func(...args)
    }
    
    const callNow = immediate && !timeout
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
    
    if (callNow) func(...args)
  }) as T
}

/**
 * 节流函数
 */
export function throttle<T extends (...args: any[]) => void>(
  func: T,
  wait: number
): T {
  let inThrottle: boolean
  let lastFunc: ReturnType<typeof setTimeout>
  let lastRan: number
  
  return ((...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      lastRan = Date.now()
      inThrottle = true
    } else {
      clearTimeout(lastFunc)
      lastFunc = setTimeout(() => {
        if (Date.now() - lastRan >= wait) {
          func(...args)
          lastRan = Date.now()
        }
      }, Math.max(wait - (Date.now() - lastRan), 0))
    }
  }) as T
}

/**
 * 转换经纬度对象格式
 */
export function normalizeLatLng(latLng: { lat: number; lng: number } | [number, number]): { lat: number; lng: number } {
  if (Array.isArray(latLng)) {
    return { lat: latLng[0], lng: latLng[1] }
  }
  return latLng
}

/**
 * 验证经纬度是否有效
 */
export function isValidLatLng(latLng: { lat: number; lng: number }): boolean {
  return (
    typeof latLng.lat === 'number' &&
    typeof latLng.lng === 'number' &&
    latLng.lat >= -90 &&
    latLng.lat <= 90 &&
    latLng.lng >= -180 &&
    latLng.lng <= 180
  )
}

/**
 * 计算两点之间的距离（米）
 */
export function getDistance(
  from: { lat: number; lng: number },
  to: { lat: number; lng: number }
): number {
  const R = 6371000 // 地球半径（米）
  const dLat = (to.lat - from.lat) * Math.PI / 180
  const dLon = (to.lng - from.lng) * Math.PI / 180
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(from.lat * Math.PI / 180) * Math.cos(to.lat * Math.PI / 180) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}

/**
 * 格式化距离显示
 */
export function formatDistance(distance: number): string {
  if (distance < 1000) {
    return `${Math.round(distance)}m`
  } else if (distance < 10000) {
    return `${(distance / 1000).toFixed(1)}km`
  } else {
    return `${Math.round(distance / 1000)}km`
  }
} 