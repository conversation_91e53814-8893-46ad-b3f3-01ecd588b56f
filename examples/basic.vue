<template>
  <div class="map-example">
    <h1>Vue3 HMap 基础示例</h1>
    
    <div class="controls">
      <div class="control-group">
        <label>API Key:</label>
        <input v-model="apiKey" placeholder="请输入华为地图API Key" />
      </div>
      
      <div class="control-group">
        <label>地图中心:</label>
        <input 
          v-model.number="center.lat" 
          type="number" 
          step="0.0001" 
          placeholder="纬度" 
        />
        <input 
          v-model.number="center.lng" 
          type="number" 
          step="0.0001" 
          placeholder="经度" 
        />
      </div>
      
      <div class="control-group">
        <label>缩放级别:</label>
        <input 
          v-model.number="zoom" 
          type="range" 
          min="2" 
          max="20" 
        />
        <span>{{ zoom }}</span>
      </div>
      
      <div class="control-group">
        <label>地图样式:</label>
        <select v-model="mapStyle">
          <option value="standard">标准</option>
          <option value="night">夜间</option>
          <option value="simple">简洁</option>
        </select>
      </div>
      
      <div class="control-group">
        <button @click="clearMarkers">清除所有标记</button>
        <button @click="addRandomMarker">添加随机标记</button>
      </div>
    </div>
    
    <div class="map-container">
      <HWMap
        v-if="apiKey"
        :api-key="apiKey"
        :center="center"
        :zoom="zoom"
        :preset-style-id="mapStyle"
        :zoom-control="true"
        :scale-control="true"
        :rotate-control="true"
        width="100%"
        height="500px"
        @ready="onMapReady"
        @error="onMapError"
        @click="onMapClick"
        @center-changed="onCenterChanged"
        @zoom-changed="onZoomChanged"
      >
        <!-- 标记 -->
        <HWMarker
          v-for="marker in markers"
          :key="marker.id"
          :position="marker.position"
          :label="marker.label"
          :draggable="marker.draggable"
          :animation="marker.animation"
          @click="onMarkerClick(marker)"
          @position-changed="onMarkerPositionChanged(marker, $event)"
        />

        <!-- 信息窗口 -->
        <HWInfoWindow
          v-if="infoWindow.visible"
          :position="infoWindow.position"
          :content="infoWindow.content"
          :visible="infoWindow.visible"
          @close="infoWindow.visible = false"
          @open="onInfoWindowOpen"
        />
      </HWMap>
      
      <div v-else class="no-api-key">
        <p>请先输入华为地图API Key</p>
        <p>
          <a href="https://developer.huawei.com/" target="_blank">
            获取API Key
          </a>
        </p>
      </div>
    </div>
    
    <div class="info-panel">
      <h3>地图信息</h3>
      <p><strong>当前中心:</strong> {{ center.lat?.toFixed(4) || '未知' }}, {{ center.lng?.toFixed(4) || '未知' }}</p>
      <p><strong>当前缩放:</strong> {{ zoom }}</p>
      <p><strong>标记数量:</strong> {{ markers.length }}</p>
      <p><strong>地图状态:</strong> {{ mapStatus }}</p>
      
      <h3>操作日志</h3>
      <div class="log-container">
        <div 
          v-for="(log, index) in logs" 
          :key="index" 
          class="log-item"
          :class="log.type"
        >
          <span class="time">{{ log.time }}</span>
          <span class="message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { HWMap as HWMapType } from '../src/types'
import { HWMap, HWMarker, HWInfoWindow } from '../src'

// 响应式数据
const apiKey = ref('temp.key') // 默认使用temp.key用于测试
const center = ref({ lat: 39.9042, lng: 116.4074 }) // 北京天安门
const zoom = ref(10)
const mapStyle = ref<'standard' | 'night' | 'simple'>('standard')
const mapInstance = ref<HWMapType | null>(null)
const mapStatus = ref('未初始化')

// 标记数据
const markers = ref<Array<{
  id: number;
  position: { lat: number; lng: number };
  label: string;
  draggable: boolean;
  animation: 'DROP' | 'BOUNCE' | null;
}>>([
  {
    id: 1,
    position: { lat: 39.9042, lng: 116.4074 },
    label: '天安门',
    draggable: false,
    animation: 'DROP'
  },
  {
    id: 2,
    position: { lat: 39.9163, lng: 116.3972 },
    label: '故宫',
    draggable: true,
    animation: null
  }
])

// 信息窗口数据
const infoWindow = ref({
  visible: false,
  position: { lat: 0, lng: 0 },
  content: ''
})

// 日志数据
const logs = ref<Array<{ time: string; message: string; type: string }>>([])

// 添加日志
function addLog(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  logs.value.unshift({
    time,
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 地图事件处理
const onMapReady = (map: HWMapType) => {
  mapInstance.value = map
  mapStatus.value = '已加载'
  addLog('地图加载完成', 'success')
}

const onMapError = (error: string) => {
  mapStatus.value = '加载失败'
  addLog(`地图加载失败: ${error}`, 'error')
}

const onMapClick = (event: any) => {
  // 在点击位置添加标记
  if (event.coordinate && event.coordinate.lat !== undefined && event.coordinate.lng !== undefined) {
    addLog(`地图点击: ${event.coordinate.lat.toFixed(4)}, ${event.coordinate.lng.toFixed(4)}`, 'info')
    
    const newMarker = {
      id: Date.now(),
      position: event.coordinate,
      label: `标记 ${markers.value.length + 1}`,
      draggable: true,
      animation: 'BOUNCE' as const
    }
    markers.value.push(newMarker)
    addLog(`添加新标记: ${newMarker.label}`, 'success')
  }
}

const onCenterChanged = (newCenter: { lat: number; lng: number }) => {
  // 验证中心点数据的有效性
  if (newCenter && 
      typeof newCenter.lat === 'number' && !isNaN(newCenter.lat) && 
      typeof newCenter.lng === 'number' && !isNaN(newCenter.lng) &&
      newCenter.lat >= -90 && newCenter.lat <= 90 &&
      newCenter.lng >= -180 && newCenter.lng <= 180) {
    
    center.value = newCenter
    addLog(`地图中心变化: ${newCenter.lat.toFixed(4)}, ${newCenter.lng.toFixed(4)}`, 'info')
  } else {
    console.warn('收到无效的中心点数据:', newCenter)
    addLog('收到无效的地图中心点数据，已忽略', 'warning')
  }
}

const onZoomChanged = (newZoom: number) => {
  // 验证缩放级别数据的有效性
  if (typeof newZoom === 'number' && !isNaN(newZoom) && newZoom >= 2 && newZoom <= 20) {
    zoom.value = newZoom
    addLog(`缩放级别变化: ${newZoom}`, 'info')
  } else {
    console.warn('收到无效的缩放级别数据:', newZoom)
    addLog('收到无效的缩放级别数据，已忽略', 'warning')
  }
}

// 标记事件处理
const onMarkerClick = (marker: any) => {
  addLog(`标记点击: ${marker.label}`, 'info')
  
  const latStr = marker.position?.lat !== undefined ? marker.position.lat.toFixed(4) : '未知'
  const lngStr = marker.position?.lng !== undefined ? marker.position.lng.toFixed(4) : '未知'
  
  infoWindow.value = {
    visible: true,
    position: marker.position,
    content: `
      <div style="padding: 10px;">
        <h4 style="margin: 0 0 8px 0; color: #333;">${marker.label}</h4>
        <p style="margin: 0; color: #666; font-size: 12px;">
          位置: ${latStr}, ${lngStr}
        </p>
        <p style="margin: 4px 0 0 0; color: #666; font-size: 12px;">
          可拖拽: ${marker.draggable ? '是' : '否'}
        </p>
      </div>
    `
  }
}

const onMarkerPositionChanged = (marker: any, newPosition: { lat: number; lng: number }) => {
  marker.position = newPosition
  if (newPosition.lat !== undefined && newPosition.lng !== undefined) {
    addLog(`标记 ${marker.label} 位置变化: ${newPosition.lat.toFixed(4)}, ${newPosition.lng.toFixed(4)}`, 'info')
  }
}

// 信息窗口事件处理
const onInfoWindowOpen = () => {
  addLog('信息窗口打开', 'info')
}

// 控制方法
const clearMarkers = () => {
  markers.value = []
  infoWindow.value.visible = false
  addLog('清除所有标记', 'warning')
}

const addRandomMarker = () => {
  // 验证当前中心点数据的有效性
  if (!center.value || 
      typeof center.value.lat !== 'number' || isNaN(center.value.lat) ||
      typeof center.value.lng !== 'number' || isNaN(center.value.lng)) {
    addLog('无法添加随机标记：地图中心点数据无效', 'error')
    return
  }
  
  const randomLat = center.value.lat + (Math.random() - 0.5) * 0.1
  const randomLng = center.value.lng + (Math.random() - 0.5) * 0.1
  
  const newMarker = {
    id: Date.now(),
    position: { lat: randomLat, lng: randomLng },
    label: `随机标记 ${markers.value.length + 1}`,
    draggable: true,
    animation: 'DROP' as const
  }
  
  markers.value.push(newMarker)
  addLog(`添加随机标记: ${newMarker.label}`, 'success')
}

// 组件挂载
onMounted(() => {
  addLog('组件初始化完成', 'success')
  
  // 尝试从本地存储加载API Key
  const savedApiKey = localStorage.getItem('vue3-hmap-api-key')
  if (savedApiKey) {
    apiKey.value = savedApiKey
  }
})

// 监听API Key变化并保存到本地存储
import { watch } from 'vue'
watch(apiKey, (newApiKey) => {
  if (newApiKey) {
    localStorage.setItem('vue3-hmap-api-key', newApiKey)
  }
})
</script>

<style scoped>
.map-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 30px;
}

.controls {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.control-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group label {
  width: 100px;
  font-weight: 500;
  color: #555;
}

.control-group input,
.control-group select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.control-group button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.control-group button:hover {
  background: #0056b3;
}

.map-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.no-api-key {
  height: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #666;
}

.no-api-key a {
  color: #007bff;
  text-decoration: none;
}

.no-api-key a:hover {
  text-decoration: underline;
}

.info-panel {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.info-panel h3 {
  color: #333;
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 2px solid #007bff;
  padding-bottom: 5px;
}

.info-panel p {
  margin: 8px 0;
  color: #555;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  background: #f8f9fa;
}

.log-item {
  padding: 8px 12px;
  border-bottom: 1px solid #e9ecef;
  font-size: 12px;
  display: flex;
  gap: 10px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.success {
  background: #d4edda;
  color: #155724;
}

.log-item.warning {
  background: #fff3cd;
  color: #856404;
}

.log-item.error {
  background: #f8d7da;
  color: #721c24;
}

.log-item .time {
  font-weight: 500;
  min-width: 60px;
}

.log-item .message {
  flex: 1;
}
</style> 