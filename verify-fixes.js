#!/usr/bin/env node

/**
 * Bug修复验证脚本
 * 检查修复是否正确应用
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 开始验证bug修复...\n');

const fixes = [
    {
        name: '事件监听器缺失',
        file: 'examples/basic.vue',
        checks: [
            { pattern: /@error="onMapError"/, description: '错误事件监听器' },
            { pattern: /@click="onMapClick"/, description: '点击事件监听器' },
            { pattern: /@center-changed="onCenterChanged"/, description: '中心点变化事件监听器' },
            { pattern: /@zoom-changed="onZoomChanged"/, description: '缩放变化事件监听器' }
        ]
    },
    {
        name: '组件被注释掉',
        file: 'examples/basic.vue',
        checks: [
            { pattern: /<HWMarker\s/, description: 'HWMarker组件未被注释' },
            { pattern: /<HWInfoWindow\s/, description: 'HWInfoWindow组件未被注释' }
        ]
    },
    {
        name: 'HWMarker事件名称错误',
        file: 'src/components/HWMarker.vue',
        checks: [
            { pattern: /addListener\('dblclick'/, description: '双击事件名称正确' },
            { pattern: /set\('dblclick'/, description: '事件监听器映射正确' }
        ]
    },
    {
        name: '属性名称不一致',
        file: 'examples/basic.vue',
        checks: [
            { pattern: /:rotate-control="true"/, description: '旋转控件属性名称正确' }
        ]
    }
];

let totalChecks = 0;
let passedChecks = 0;

fixes.forEach((fix, index) => {
    console.log(`${index + 1}. 检查修复: ${fix.name}`);
    
    const filePath = path.join(__dirname, fix.file);
    
    if (!fs.existsSync(filePath)) {
        console.log(`   ❌ 文件不存在: ${fix.file}`);
        return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    fix.checks.forEach(check => {
        totalChecks++;
        if (check.pattern.test(content)) {
            console.log(`   ✅ ${check.description}`);
            passedChecks++;
        } else {
            console.log(`   ❌ ${check.description}`);
        }
    });
    
    console.log('');
});

// 检查是否还有被注释的组件
console.log('🔍 检查是否还有遗留的注释组件...');
const basicVueContent = fs.readFileSync(path.join(__dirname, 'examples/basic.vue'), 'utf8');

const commentedComponents = [
    /<!--\s*<HWMarker/g,
    /<!--\s*<HWInfoWindow/g
];

let hasCommentedComponents = false;
commentedComponents.forEach(pattern => {
    const matches = basicVueContent.match(pattern);
    if (matches && matches.length > 0) {
        console.log(`   ❌ 发现 ${matches.length} 个被注释的组件`);
        hasCommentedComponents = true;
    }
});

if (!hasCommentedComponents) {
    console.log('   ✅ 没有发现被注释的组件');
}

console.log('\n📊 修复验证结果:');
console.log(`   总检查项: ${totalChecks}`);
console.log(`   通过检查: ${passedChecks}`);
console.log(`   成功率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

if (passedChecks === totalChecks) {
    console.log('\n🎉 所有bug修复验证通过！');
    process.exit(0);
} else {
    console.log('\n⚠️  部分修复可能存在问题，请检查上述失败项');
    process.exit(1);
}
