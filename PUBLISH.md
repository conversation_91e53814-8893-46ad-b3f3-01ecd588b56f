# Vue3 HMap 发布指南

本文档说明如何将 Vue3 HMap 组件库发布到 npm 仓库。

## 📋 发布前准备

### 1. 检查项目状态

确保所有代码已提交并推送到 Git 仓库：

```bash
git status
git add .
git commit -m "feat: 完成基础组件库功能"
git push origin main
```

### 2. 安装依赖

使用 pnpm 安装所有依赖：

```bash
pnpm install
```

### 3. 构建检查

确保项目可以正常构建：

```bash
# 类型检查
pnpm type-check

# 构建库文件
pnpm build:lib
```

构建成功后，检查 `dist` 目录是否包含：
- `index.js` - ES Module 格式
- `index.cjs.js` - CommonJS 格式  
- `index.umd.js` - UMD 格式
- `index.d.ts` - TypeScript 类型定义

### 4. 更新版本信息

根据变更类型更新版本号：

```bash
# 补丁版本 (bug 修复)
npm version patch

# 次要版本 (新功能)
npm version minor

# 主要版本 (破坏性变更)
npm version major
```

或手动编辑 `package.json` 中的 `version` 字段。

## 🚀 发布流程

### 1. 登录 npm

确保已登录 npm 账号：

```bash
npm whoami
```

如果未登录，执行：

```bash
npm login
```

### 2. 发布到 npm

#### 首次发布

```bash
npm publish
```

#### 发布预发布版本

```bash
# 发布 beta 版本
npm publish --tag beta

# 发布 alpha 版本  
npm publish --tag alpha
```

#### 发布到指定 registry

```bash
npm publish --registry https://registry.npmjs.org/
```

### 3. 验证发布

发布成功后：

1. 访问 [npmjs.com](https://www.npmjs.com/) 搜索 `vue3-hmap`
2. 检查版本号是否正确
3. 验证文件是否完整上传

### 4. 测试安装

在新项目中测试安装：

```bash
mkdir test-vue3-hmap
cd test-vue3-hmap
npm init -y
npm install vue3-hmap
```

## 📝 版本管理

### 语义化版本

遵循 [语义化版本规范](https://semver.org/lang/zh-CN/)：

- **主版本号**：不兼容的 API 修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

### 版本标签

使用不同标签发布不同稳定性的版本：

- `latest` (默认)：稳定版本
- `beta`：测试版本
- `alpha`：开发版本
- `next`：下一个主版本的预览

### 更新最新标签

```bash
# 将特定版本设为最新
npm dist-tag add vue3-hmap@1.0.1 latest
```

## 🔄 更新发布

### 1. 开发新功能

在新分支开发：

```bash
git checkout -b feature/new-component
# 开发新功能
git add .
git commit -m "feat: 添加新组件"
git push origin feature/new-component
```

### 2. 合并到主分支

```bash
git checkout main
git merge feature/new-component
```

### 3. 更新版本并发布

```bash
npm version minor
pnpm build:lib
npm publish
git push origin main --tags
```

## 📦 发布检查清单

发布前请确认：

- [ ] 所有测试通过
- [ ] 类型检查无错误
- [ ] 构建成功
- [ ] README 文档更新
- [ ] CHANGELOG 更新
- [ ] 版本号正确
- [ ] package.json 信息完整
- [ ] 所有文件已提交到 Git

## 🛠️ 发布脚本

可以创建发布脚本自动化流程：

```bash
# scripts/publish.sh
#!/bin/bash

set -e

echo "🔍 检查工作目录..."
if [[ -n $(git status --porcelain) ]]; then
  echo "❌ 工作目录不干净，请先提交所有更改"
  exit 1
fi

echo "🔧 运行类型检查..."
pnpm type-check

echo "🏗️ 构建项目..."
pnpm build:lib

echo "📋 请选择版本类型:"
echo "1) patch (修复)"
echo "2) minor (功能)"
echo "3) major (破坏性)"
read -p "请输入选择 (1-3): " choice

case $choice in
  1) npm version patch ;;
  2) npm version minor ;;
  3) npm version major ;;
  *) echo "❌ 无效选择"; exit 1 ;;
esac

echo "🚀 发布到 npm..."
npm publish

echo "📤 推送标签到 Git..."
git push origin main --tags

echo "✅ 发布完成！"
```

使用脚本：

```bash
chmod +x scripts/publish.sh
./scripts/publish.sh
```

## 🔒 私有包发布

如果要发布私有包：

```bash
# 发布到私有 registry
npm publish --registry https://your-private-registry.com/

# 或者设置 .npmrc
echo "registry=https://your-private-registry.com/" > .npmrc
npm publish
```

## 📞 支持

如果在发布过程中遇到问题：

1. 检查 npm 账号权限
2. 确认包名没有冲突
3. 验证网络连接
4. 查看 npm 日志：`npm config get registry`

更多信息请参考 [npm 官方文档](https://docs.npmjs.com/cli/v8/commands/npm-publish)。 