/// <reference types="vite/client" />

// Vue单文件组件的类型声明
declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// 环境变量的类型声明
interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_HUAWEI_MAP_API_KEY: string
  // 可以添加更多环境变量
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}

// 华为地图SDK的全局声明
declare global {
  interface Window {
    HWMapJsSDK: any
  }
}

export {} 