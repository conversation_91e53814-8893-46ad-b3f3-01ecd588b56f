#!/usr/bin/env node

/**
 * 测试华为地图SDK回调函数修复
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔍 检查华为地图SDK回调函数修复...\n');

const utilsPath = path.join(__dirname, 'src/utils/index.ts');
const utilsContent = fs.readFileSync(utilsPath, 'utf8');

// 检查修复项
const checks = [
    {
        name: 'URL回调参数',
        pattern: /callback=initMap/,
        description: 'URL中的callback参数应该是initMap'
    },
    {
        name: '全局回调函数名',
        pattern: /window.*\.initMap\s*=/,
        description: '全局回调函数名应该是initMap'
    },
    {
        name: '超时清理函数名',
        pattern: /delete.*initMap/,
        description: '超时清理中的函数名应该是initMap'
    },
    {
        name: '错误处理清理函数名',
        pattern: /onerror[\s\S]*?delete.*initMap/,
        description: '错误处理中的清理函数名应该是initMap'
    }
];

let passedChecks = 0;
const totalChecks = checks.length;

checks.forEach((check, index) => {
    console.log(`${index + 1}. 检查: ${check.name}`);
    
    if (check.pattern.test(utilsContent)) {
        console.log(`   ✅ ${check.description}`);
        passedChecks++;
    } else {
        console.log(`   ❌ ${check.description}`);
    }
});

// 检查是否还有旧的函数名
console.log('\n🔍 检查是否还有遗留的错误函数名...');
const oldFunctionPattern = /initHWMapSDK/g;
const matches = utilsContent.match(oldFunctionPattern);

if (matches && matches.length > 0) {
    console.log(`   ❌ 发现 ${matches.length} 个遗留的 initHWMapSDK 引用`);
} else {
    console.log('   ✅ 没有发现遗留的错误函数名');
    passedChecks++;
}

console.log('\n📊 修复验证结果:');
console.log(`   总检查项: ${totalChecks + 1}`);
console.log(`   通过检查: ${passedChecks}`);
console.log(`   成功率: ${((passedChecks / (totalChecks + 1)) * 100).toFixed(1)}%`);

if (passedChecks === totalChecks + 1) {
    console.log('\n🎉 华为地图SDK回调函数修复验证通过！');
    console.log('💡 现在地图初始化完成后应该不会再卡住了。');
    process.exit(0);
} else {
    console.log('\n⚠️  修复可能存在问题，请检查上述失败项');
    process.exit(1);
}
