<template>
  <div 
    ref="mapContainer"
    :id="containerId"
    class="hw-map-container"
    :style="{ width: width, height: height }"
  >
    <div v-if="loading" class="hw-map-loading">
      <slot name="loading">
        <div class="hw-map-loading-spinner">
          <div class="spinner"></div>
          <span>地图加载中...</span>
        </div>
      </slot>
    </div>
    <div v-if="error" class="hw-map-error">
      <slot name="error" :error="error">
        <div class="hw-map-error-content">
          <h4>🚫 地图加载失败</h4>
          <p>{{ error }}</p>
          <div class="hw-map-error-tips">
            <h5>💡 常见解决方案：</h5>
            <ul>
              <li>检查API Key是否正确</li>
              <li>确认网络连接正常</li>
              <li>检查控制台是否有更多错误信息</li>
            </ul>
          </div>
          <button @click="reload" class="hw-map-retry-btn">🔄 重新加载</button>
        </div>
      </slot>
    </div>
    <!-- 地图子组件插槽 -->
    <template v-if="mapInstance">
      <slot :map="mapInstance" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, provide } from 'vue'
import type { HWMap as HWMapType, MapOptions } from '@/types'
import { getHWMapSDK, loadHWMapSDK, createUniqueId, deepMerge, isValidLatLng } from '@/utils'

interface Props {
  /** 华为地图 API Key */
  apiKey: string
  /** 地图中心点 */
  center: { lat: number; lng: number }
  /** 地图缩放级别 */
  zoom?: number
  /** 地图宽度 */
  width?: string
  /** 地图高度 */
  height?: string
  /** 地图选项 */
  options?: Partial<MapOptions>
  /** 访问令牌 */
  accessToken?: string
  /** 地图语言 */
  language?: string
  /** 地图类型 */
  mapType?: 'ROADMAP' | 'TERRAIN'
  /** 预设样式 */
  presetStyleId?: 'standard' | 'night' | 'simple'
  /** 是否显示缩放控件 */
  zoomControl?: boolean
  /** 是否显示比例尺 */
  scaleControl?: boolean
  /** 是否显示指北针 */
  rotateControl?: boolean
  /** 是否显示版权信息 */
  copyrightControl?: boolean
  /** 是否显示位置控件 */
  locationControl?: boolean
  /** 是否显示导航控件 */
  navigationControl?: boolean
  /** 最小缩放级别 */
  minZoom?: number
  /** 最大缩放级别 */
  maxZoom?: number
  /** 瓦片类型 */
  sourceType?: 'vector' | 'raster'
}

interface Emits {
  (event: 'ready', map: HWMapType): void
  (event: 'error', error: string): void
  (event: 'click', clickEvent: any): void
  (event: 'dblclick', dblclickEvent: any): void
  (event: 'contextmenu', contextmenuEvent: any): void
  (event: 'center-changed', center: { lat: number; lng: number }): void
  (event: 'zoom-changed', zoom: number): void
  (event: 'heading-changed', heading: number): void
  (event: 'move-start'): void
  (event: 'move-end'): void
}

const props = withDefaults(defineProps<Props>(), {
  zoom: 10,
  width: '100%',
  height: '400px',
  language: 'zh',
  mapType: 'ROADMAP',
  presetStyleId: 'standard',
  zoomControl: true,
  scaleControl: false,
  rotateControl: false,
  copyrightControl: false,
  locationControl: false,
  navigationControl: false,
  minZoom: 2,
  maxZoom: 20,
  sourceType: 'vector'
})

const emit = defineEmits<Emits>()

// 响应式状态
const mapContainer = ref<HTMLElement>()
const mapInstance = ref<HWMapType | null>(null)
const loading = ref(true)
const error = ref<string>('')
const containerId = createUniqueId('hw-map')

// 提供地图实例给子组件
provide('hwMap', mapInstance)

// 地图事件监听器
const eventListeners = new Map<string, Function>()

/**
 * 创建地图实例
 */
async function createMap(): Promise<void> {
  try {
    loading.value = true
    error.value = ''
    console.log('🗺️ 开始创建华为地图实例...')

    // 检查容器是否存在
    if (!mapContainer.value) {
      throw new Error('地图容器不存在')
    }
    console.log('✅ 地图容器检查通过')

    // 验证中心点坐标
    if (!isValidLatLng(props.center)) {
      throw new Error(`无效的中心点坐标: lat=${props.center.lat}, lng=${props.center.lng}`)
    }
    console.log('✅ 中心点坐标验证通过:', props.center)

    // 验证API Key
    if (!props.apiKey || props.apiKey.length === 0) {
      throw new Error('API Key 不能为空')
    }
    console.log('✅ API Key 验证通过')

    // 加载华为地图 SDK
    console.log('🔄 正在加载华为地图 SDK...')
    await loadHWMapSDK(props.apiKey)
    console.log('✅ 华为地图 SDK 加载完成')

    const SDK = getHWMapSDK()
    if (!SDK) {
      throw new Error('华为地图 SDK 实例获取失败')
    }
    console.log('✅ 华为地图 SDK 实例获取成功')

    // 创建地图选项
    const mapOptions: MapOptions = deepMerge({
      center: props.center,
      zoom: props.zoom,
      language: props.language,
      mapType: props.mapType,
      presetStyleId: props.presetStyleId,
      zoomControl: props.zoomControl,
      scaleControl: props.scaleControl,
      rotateControl: props.rotateControl,
      copyrightControl: props.copyrightControl,
      locationControl: props.locationControl,
      navigationControl: props.navigationControl,
      minZoom: props.minZoom,
      maxZoom: props.maxZoom,
      sourceType: props.sourceType,
      authOptions: props.accessToken ? { accessToken: props.accessToken } : undefined
    }, props.options || {})

    console.log('🔄 正在创建地图实例...', mapOptions)
    
    // 创建地图实例
    mapInstance.value = new SDK.HWMap(mapContainer.value, mapOptions)
    console.log('✅ 地图实例创建成功')

    // 绑定事件监听器
    bindEventListeners()
    console.log('✅ 事件监听器绑定完成')

    loading.value = false
    emit('ready', mapInstance.value)
    console.log('🎉 华为地图初始化完成！')
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : '未知错误'
    console.error('❌ 华为地图创建失败:', errorMessage)
    console.error('❌ 详细错误信息:', err)
    
    error.value = errorMessage
    loading.value = false
    emit('error', errorMessage)
  }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners(): void {
  if (!mapInstance.value) return

  const map = mapInstance.value

  // 点击事件
  const clickHandler = (event: any) => emit('click', event)
  map.on('click', clickHandler)
  eventListeners.set('click', clickHandler)

  // 双击事件
  const dblclickHandler = (event: any) => emit('dblclick', event)
  map.on('dblclick', dblclickHandler)
  eventListeners.set('dblclick', dblclickHandler)

  // 右键菜单事件
  const contextmenuHandler = (event: any) => emit('contextmenu', event)
  map.on('contextmenu', contextmenuHandler)
  eventListeners.set('contextmenu', contextmenuHandler)

  // 中心点变化事件
  const centerChangedHandler = (event: any) => {
    // 从地图实例中获取当前的中心点
    const currentCenter = map.getCenter()
    console.log('🔄 地图中心点原始数据:', currentCenter)
    
    if (currentCenter) {
      // 处理不同的坐标格式
      let normalizedCenter: { lat: number; lng: number } | null = null
      
      if (Array.isArray(currentCenter) && currentCenter.length >= 2) {
        // 如果是数组格式 [lng, lat]
        normalizedCenter = { lat: currentCenter[1], lng: currentCenter[0] }
      } else if ((currentCenter as any).lat !== undefined && (currentCenter as any).lng !== undefined) {
        // 如果是对象格式 {lat, lng}
        normalizedCenter = { lat: (currentCenter as any).lat, lng: (currentCenter as any).lng }
      }
      
      if (normalizedCenter && 
          typeof normalizedCenter.lat === 'number' && !isNaN(normalizedCenter.lat) &&
          typeof normalizedCenter.lng === 'number' && !isNaN(normalizedCenter.lng)) {
        console.log('✅ 标准化后的中心点:', normalizedCenter)
        emit('center-changed', normalizedCenter)
      } else {
        console.warn('⚠️ 无法解析的中心点格式:', currentCenter)
      }
    }
  }
  map.onCenterChanged(centerChangedHandler)
  eventListeners.set('center-changed', centerChangedHandler)

  // 缩放级别变化事件
  const zoomChangedHandler = (event: any) => {
    // 从地图实例中获取当前的缩放级别
    const currentZoom = map.getZoom()
    if (typeof currentZoom === 'number') {
      console.log('🔄 地图缩放级别变化事件:', currentZoom)
      emit('zoom-changed', currentZoom)
    }
  }
  map.onZoomChanged(zoomChangedHandler)
  eventListeners.set('zoom-changed', zoomChangedHandler)

  // 方向变化事件
  const headingChangedHandler = (heading: number) => {
    emit('heading-changed', heading)
  }
  map.onHeadingChanged(headingChangedHandler)
  eventListeners.set('heading-changed', headingChangedHandler)

  // 移动开始事件
  const moveStartHandler = () => emit('move-start')
  map.on('movestart', moveStartHandler)
  eventListeners.set('movestart', moveStartHandler)

  // 移动结束事件
  const moveEndHandler = () => emit('move-end')
  map.on('moveend', moveEndHandler)
  eventListeners.set('moveend', moveEndHandler)
}

/**
 * 解绑事件监听器
 */
function unbindEventListeners(): void {
  if (!mapInstance.value) return

  const map = mapInstance.value
  eventListeners.forEach((handler, event) => {
    map.un(event, handler)
  })
  eventListeners.clear()
}

/**
 * 重新加载地图
 */
async function reload(): Promise<void> {
  if (mapInstance.value) {
    unbindEventListeners()
    mapInstance.value = null
  }
  await nextTick()
  await createMap()
}

/**
 * 设置地图中心点
 */
function setCenter(center: { lat: number; lng: number }): void {
  if (mapInstance.value && isValidLatLng(center)) {
    mapInstance.value.setCenter(center)
  }
}

/**
 * 设置地图缩放级别
 */
function setZoom(zoom: number): void {
  if (mapInstance.value && zoom >= props.minZoom && zoom <= props.maxZoom) {
    mapInstance.value.setZoom(zoom)
  }
}

/**
 * 获取地图中心点
 */
function getCenter(): { lat: number; lng: number } | null {
  return mapInstance.value?.getCenter() || null
}

/**
 * 获取地图缩放级别
 */
function getZoom(): number | null {
  return mapInstance.value?.getZoom() || null
}

/**
 * 重新调整地图大小
 */
function resize(): void {
  if (mapInstance.value) {
    mapInstance.value.resize()
  }
}

// 监听属性变化
watch(
  () => props.center,
  (newCenter: { lat: number; lng: number }) => {
    if (mapInstance.value && isValidLatLng(newCenter)) {
      setCenter(newCenter)
    }
  },
  { deep: true }
)

watch(
  () => props.zoom,
  (newZoom: number | undefined) => {
    if (mapInstance.value && newZoom !== undefined) {
      setZoom(newZoom)
    }
  }
)

// 暴露方法给父组件
defineExpose({
  map: mapInstance,
  setCenter,
  setZoom,
  getCenter,
  getZoom,
  resize,
  reload
})

// 生命周期
onMounted(() => {
  createMap()
})

onUnmounted(() => {
  unbindEventListeners()
})
</script>

<style scoped>
.hw-map-container {
  position: relative;
  overflow: hidden;
}

.hw-map-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.hw-map-loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.hw-map-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.hw-map-error-content {
  text-align: left;
  padding: 24px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 480px;
  border: 1px solid #e74c3c;
}

.hw-map-error-content h4 {
  margin: 0 0 12px 0;
  color: #e74c3c;
  font-size: 18px;
  font-weight: 600;
}

.hw-map-error-content p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #e74c3c;
}

.hw-map-error-tips {
  margin-bottom: 20px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #3498db;
}

.hw-map-error-tips h5 {
  margin: 0 0 8px 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.hw-map-error-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #666;
  font-size: 13px;
}

.hw-map-error-tips li {
  margin-bottom: 4px;
  line-height: 1.4;
}

.hw-map-retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
  width: 100%;
}

.hw-map-retry-btn:hover {
  background: #2980b9;
}
</style> 