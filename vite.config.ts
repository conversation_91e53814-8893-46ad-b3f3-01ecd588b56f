import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'
  
  return {
    plugins: [vue()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    build: isLib ? {
      lib: {
        entry: resolve(fileURLToPath(new URL('.', import.meta.url)), 'src/index.ts'),
        name: 'Vue3HMap',
        fileName: (format) => `index.${format}.js`,
        formats: ['es', 'cjs', 'umd']
      },
      rollupOptions: {
        external: ['vue'],
        output: {
          globals: {
            vue: 'Vue'
          },
          exports: 'named'
        }
      }
    } : {
      rollupOptions: {
        input: {
          main: resolve(fileURLToPath(new URL('.', import.meta.url)), 'index.html'),
        },
      },
    },
  }
}) 