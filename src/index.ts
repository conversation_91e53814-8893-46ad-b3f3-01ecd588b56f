import type { App } from 'vue'
import HWMap from './components/HWMap.vue'
import HWMarker from './components/HWMarker.vue'
import HWInfoWindow from './components/HWInfoWindow.vue'

// 导出所有组件
export { HWMap, HWMarker, HWInfoWindow }

// 导出类型
export * from './types'

// 导出工具函数
export * from './utils'

// 组件列表
const components = [
  HWMap,
  HWMarker,
  HWInfoWindow
]

// 定义安装函数
const install = (app: App) => {
  components.forEach(component => {
    app.component(component.name || component.__name, component)
  })
}

// 创建插件对象
const Vue3HMap = {
  install,
  HWMap,
  HWMarker,
  HWInfoWindow
}

// 默认导出
export default Vue3HMap

// 支持按需导入
export const setupVue3HMap = install

// 版本信息
export const version = '0.1.0'

// 全局安装（如果通过 script 标签引入）
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}

// 类型声明
declare module '@vue/runtime-core' {
  interface GlobalComponents {
    HWMap: typeof HWMap
    HWMarker: typeof HWMarker
    HWInfoWindow: typeof HWInfoWindow
  }
} 