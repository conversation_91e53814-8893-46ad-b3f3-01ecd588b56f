<template>
  <div id="app">
    <header class="app-header">
      <h1>🗺️ Vue3 HMap 开发环境</h1>
      <p>基于华为地图的Vue3组件库开发测试</p>
    </header>

    <main class="app-main">
      <!-- 开发测试区域 -->
      <section class="test-section">
        <h2>组件测试</h2>
        
        <div class="controls">
          <div class="control-group">
            <label>API Key:</label>
            <input 
              v-model="config.apiKey" 
              type="text" 
              placeholder="请输入华为地图API Key"
              class="api-input"
            />
            <button @click="saveApiKey" class="btn-save">保存</button>
          </div>
          
          <div class="control-group">
            <label>地图中心:</label>
            <input 
              v-model.number="config.center.lat" 
              type="number" 
              step="0.0001" 
              placeholder="纬度"
            />
            <input 
              v-model.number="config.center.lng" 
              type="number" 
              step="0.0001" 
              placeholder="经度" 
            />
          </div>
          
          <div class="control-group">
            <label>缩放级别:</label>
            <input 
              v-model.number="config.zoom" 
              type="range" 
              min="2" 
              max="20"
            />
            <span class="zoom-value">{{ config.zoom }}</span>
          </div>
          
          <div class="control-group">
            <label>地图样式:</label>
            <select v-model="config.presetStyleId">
              <option value="standard">标准</option>
              <option value="night">夜间</option>
              <option value="simple">简洁</option>
            </select>
          </div>
        </div>

        <!-- 地图容器 -->
        <div class="map-wrapper">
          <div v-if="!config.apiKey" class="no-api-key">
            <h3>🔑 需要API Key</h3>
            <p>请先输入华为地图API Key以开始测试</p>
            <p>
              <a href="https://developer.huawei.com/" target="_blank" rel="noopener">
                📖 获取API Key指南
              </a>
            </p>
          </div>
          
          <HWMap
            v-else
            :api-key="config.apiKey"
            :center="config.center"
            :zoom="config.zoom"
            :preset-style-id="config.presetStyleId"
            :zoom-control="true"
            :scale-control="true"
            :rotate-control="true"
            :location-control="false"
            :navigation-control="false"
            width="100%"
            height="500px"
            @ready="onMapReady"
            @error="onMapError"
            @click="onMapClick"
            @center-changed="onCenterChanged"
            @zoom-changed="onZoomChanged"
          >
            <!-- 预设标记 -->
            <HWMarker
              v-for="marker in markers"
              :key="marker.id"
              :position="marker.position"
              :label="marker.label"
              :draggable="marker.draggable"
              :animation="marker.animation"
              :visible="marker.visible"
              @click="onMarkerClick(marker)"
              @position-changed="onMarkerPositionChanged(marker, $event)"
            />
            
            <!-- 信息窗口 -->
            <HWInfoWindow
              v-if="infoWindow.visible"
              :position="infoWindow.position"
              :content="infoWindow.content"
              :visible="infoWindow.visible"
              @close="closeInfoWindow"
              @open="onInfoWindowOpen"
            />
          </HWMap>
        </div>

        <!-- 操作按钮 -->
        <div class="actions" v-if="config.apiKey">
          <button @click="addRandomMarker" class="btn-action">🎯 添加随机标记</button>
          <button @click="clearAllMarkers" class="btn-action">🗑️ 清除所有标记</button>
          <button @click="centerToBeijing" class="btn-action">🏛️ 回到北京</button>
          <button @click="toggleMarkerVisibility" class="btn-action">
            {{ allMarkersVisible ? '👁️ 隐藏标记' : '👁️‍🗨️ 显示标记' }}
          </button>
        </div>

        <!-- 状态信息 -->
        <div class="status-panel">
          <h3>📊 状态信息</h3>
          <div class="status-grid">
            <div class="status-item">
              <label>地图状态:</label>
              <span :class="['status-value', mapStatus.type]">{{ mapStatus.text }}</span>
            </div>
            <div class="status-item">
              <label>当前中心:</label>
              <span class="status-value">
                {{ config.center.lat?.toFixed(4) || '未知' }}, {{ config.center.lng?.toFixed(4) || '未知' }}
              </span>
            </div>
            <div class="status-item">
              <label>当前缩放:</label>
              <span class="status-value">{{ config.zoom }}</span>
            </div>
            <div class="status-item">
              <label>标记数量:</label>
              <span class="status-value">{{ markers.length }}</span>
            </div>
          </div>
        </div>

        <!-- 操作日志 -->
        <div class="log-panel">
          <h3>📝 操作日志</h3>
          <div class="log-container">
            <div 
              v-for="(log, index) in logs" 
              :key="index"
              :class="['log-item', log.type]"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            <div v-if="logs.length === 0" class="log-empty">
              暂无日志记录
            </div>
          </div>
          <button @click="clearLogs" class="btn-clear-logs">清除日志</button>
        </div>
      </section>
    </main>

    <footer class="app-footer">
      <p>💡 这是Vue3 HMap组件库的开发测试环境</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import type { HWMap } from './types'
import { HWMap as HWMapComponent, HWMarker, HWInfoWindow } from './index'

// 重命名组件以避免与类型冲突
const HWMap = HWMapComponent

// 配置数据
const config = reactive({
  apiKey: '',
  center: { lat: 39.9042, lng: 116.4074 }, // 北京天安门
  zoom: 10,
  presetStyleId: 'standard' as 'standard' | 'night' | 'simple'
})

// 地图实例和状态
const mapInstance = ref<HWMap | null>(null)
const mapStatus = reactive({
  text: '未初始化',
  type: 'info'
})

// 标记数据
const markers = ref([
  {
    id: 1,
    position: { lat: 39.9042, lng: 116.4074 },
    label: '天安门广场',
    draggable: false,
    animation: 'DROP' as const,
    visible: true
  },
  {
    id: 2,
    position: { lat: 39.9163, lng: 116.3972 },
    label: '故宫博物院',
    draggable: true,
    animation: null,
    visible: true
  },
  {
    id: 3,
    position: { lat: 39.9056, lng: 116.3958 },
    label: '中山公园',
    draggable: true,
    animation: 'BOUNCE' as const,
    visible: true
  }
])

// 信息窗口数据
const infoWindow = reactive({
  visible: false,
  position: { lat: 0, lng: 0 },
  content: ''
})

// 日志数据
const logs = ref<Array<{
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}>>([])

// 计算属性
const allMarkersVisible = computed(() => 
  markers.value.every(marker => marker.visible)
)

// 工具函数
function addLog(message: string, type: 'info' | 'success' | 'warning' | 'error' = 'info') {
  const now = new Date()
  const time = now.toLocaleTimeString()
  
  logs.value.unshift({
    time,
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

function saveApiKey() {
  if (config.apiKey.trim()) {
    localStorage.setItem('vue3-hmap-api-key', config.apiKey.trim())
    addLog('API Key已保存', 'success')
  } else {
    addLog('请输入有效的API Key', 'warning')
  }
}

// 地图事件处理
function onMapReady(map: HWMap) {
  mapInstance.value = map
  mapStatus.text = '已就绪'
  mapStatus.type = 'success'
  addLog('地图加载完成', 'success')
}

function onMapError(error: string) {
  mapStatus.text = '加载失败'
  mapStatus.type = 'error'
  addLog(`地图加载失败: ${error}`, 'error')
}

function onMapClick(event: any) {
  const coord = event.coordinate
  if (coord && coord.lat !== undefined && coord.lng !== undefined) {
    addLog(`地图点击: ${coord.lat.toFixed(4)}, ${coord.lng.toFixed(4)}`, 'info')
    
    // 在点击位置添加新标记
    const newMarker = {
      id: Date.now(),
      position: coord,
      label: `点击标记 ${markers.value.length + 1}`,
      draggable: true,
      animation: 'BOUNCE' as const,
      visible: true
    }
    markers.value.push(newMarker)
    addLog(`添加新标记: ${newMarker.label}`, 'success')
  }
}

function onCenterChanged(center: { lat: number; lng: number }) {
  // 验证中心点数据的有效性
  if (center && 
      typeof center.lat === 'number' && !isNaN(center.lat) && 
      typeof center.lng === 'number' && !isNaN(center.lng) &&
      center.lat >= -90 && center.lat <= 90 &&
      center.lng >= -180 && center.lng <= 180) {
    
    config.center.lat = center.lat
    config.center.lng = center.lng
    addLog(`地图中心变化: ${center.lat.toFixed(4)}, ${center.lng.toFixed(4)}`, 'info')
  } else {
    console.warn('收到无效的中心点数据:', center)
    addLog('收到无效的地图中心点数据，已忽略', 'warning')
  }
}

function onZoomChanged(zoom: number) {
  // 验证缩放级别数据的有效性
  if (typeof zoom === 'number' && !isNaN(zoom) && zoom >= 2 && zoom <= 20) {
    config.zoom = zoom
    addLog(`缩放级别变化: ${zoom}`, 'info')
  } else {
    console.warn('收到无效的缩放级别数据:', zoom)
    addLog('收到无效的缩放级别数据，已忽略', 'warning')
  }
}

// 标记事件处理
function onMarkerClick(marker: any) {
  addLog(`标记点击: ${marker.label}`, 'info')
  
  infoWindow.visible = true
  infoWindow.position = marker.position
  
  const latStr = marker.position?.lat !== undefined ? marker.position.lat.toFixed(4) : '未知'
  const lngStr = marker.position?.lng !== undefined ? marker.position.lng.toFixed(4) : '未知'
  
  infoWindow.content = `
    <div style="padding: 12px; min-width: 200px;">
      <h4 style="margin: 0 0 8px 0; color: #333; font-size: 16px;">${marker.label}</h4>
      <div style="color: #666; font-size: 14px; line-height: 1.5;">
        <p style="margin: 4px 0;"><strong>位置:</strong> ${latStr}, ${lngStr}</p>
        <p style="margin: 4px 0;"><strong>可拖拽:</strong> ${marker.draggable ? '是' : '否'}</p>
        <p style="margin: 4px 0;"><strong>动画:</strong> ${marker.animation || '无'}</p>
        <p style="margin: 4px 0;"><strong>ID:</strong> ${marker.id}</p>
      </div>
    </div>
  `
}

function onMarkerPositionChanged(marker: any, newPosition: { lat: number; lng: number }) {
  marker.position = newPosition
  if (newPosition.lat !== undefined && newPosition.lng !== undefined) {
    addLog(`标记 "${marker.label}" 移动到: ${newPosition.lat.toFixed(4)}, ${newPosition.lng.toFixed(4)}`, 'info')
  }
}

// 信息窗口事件处理
function onInfoWindowOpen() {
  addLog('信息窗口打开', 'info')
}

function closeInfoWindow() {
  infoWindow.visible = false
  addLog('信息窗口关闭', 'info')
}

// 操作方法
function addRandomMarker() {
  // 验证当前中心点数据的有效性
  if (!config.center || 
      typeof config.center.lat !== 'number' || isNaN(config.center.lat) ||
      typeof config.center.lng !== 'number' || isNaN(config.center.lng)) {
    addLog('无法添加随机标记：地图中心点数据无效', 'error')
    return
  }
  
  const randomLat = config.center.lat + (Math.random() - 0.5) * 0.02
  const randomLng = config.center.lng + (Math.random() - 0.5) * 0.02
  
  const newMarker = {
    id: Date.now(),
    position: { lat: randomLat, lng: randomLng },
    label: `随机标记 ${markers.value.length + 1}`,
    draggable: true,
    animation: Math.random() > 0.5 ? 'DROP' as const : 'BOUNCE' as const,
    visible: true
  }
  
  markers.value.push(newMarker)
  addLog(`添加随机标记: ${newMarker.label}`, 'success')
}

function clearAllMarkers() {
  const count = markers.value.length
  markers.value = []
  infoWindow.visible = false
  addLog(`清除了 ${count} 个标记`, 'warning')
}

function centerToBeijing() {
  // 重置为安全的默认值
  config.center = { lat: 39.9042, lng: 116.4074 }
  config.zoom = 10
  addLog('回到北京中心', 'info')
}

// 数据安全检查和重置函数
function validateAndResetConfig() {
  // 检查并修复center数据
  if (!config.center || 
      typeof config.center.lat !== 'number' || isNaN(config.center.lat) ||
      typeof config.center.lng !== 'number' || isNaN(config.center.lng)) {
    console.warn('检测到无效的中心点数据，重置为默认值')
    config.center = { lat: 39.9042, lng: 116.4074 }
    addLog('中心点数据已重置为北京天安门', 'warning')
  }
  
  // 检查并修复zoom数据
  if (typeof config.zoom !== 'number' || isNaN(config.zoom) || config.zoom < 2 || config.zoom > 20) {
    console.warn('检测到无效的缩放级别数据，重置为默认值')
    config.zoom = 10
    addLog('缩放级别已重置为默认值', 'warning')
  }
}

function toggleMarkerVisibility() {
  const newVisibility = !allMarkersVisible.value
  markers.value.forEach(marker => {
    marker.visible = newVisibility
  })
  addLog(`${newVisibility ? '显示' : '隐藏'}所有标记`, 'info')
}

function clearLogs() {
  logs.value = []
  addLog('日志已清除', 'info')
}

// 组件挂载
onMounted(() => {
  // 验证和重置配置数据
  validateAndResetConfig()
  
  // 尝试从本地存储加载API Key
  const savedApiKey = localStorage.getItem('vue3-hmap-api-key')
  if (savedApiKey) {
    config.apiKey = savedApiKey
  }
  
  addLog('开发环境初始化完成', 'success')
  addLog('点击地图可添加标记，拖拽标记可移动位置', 'info')
  
  // 定期检查数据完整性（每30秒）
  setInterval(validateAndResetConfig, 30000)
})
</script>

<style scoped>
/* 全局样式 */
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: #333;
  line-height: 1.6;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 头部样式 */
.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 2rem;
  text-align: center;
  color: white;
}

.app-header h1 {
  margin: 0 0 0.5rem;
  font-size: 2.5rem;
  font-weight: 300;
}

.app-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* 主要内容样式 */
.app-main {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.test-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.test-section h2 {
  margin: 0;
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
  font-size: 1.5rem;
}

/* 控制面板样式 */
.controls {
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.control-group {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.control-group:last-child {
  margin-bottom: 0;
}

.control-group label {
  min-width: 100px;
  font-weight: 500;
  color: #495057;
}

.control-group input,
.control-group select {
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.control-group input:focus,
.control-group select:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.api-input {
  min-width: 300px;
}

.btn-save {
  padding: 0.5rem 1rem;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-save:hover {
  background: #218838;
}

.zoom-value {
  min-width: 30px;
  font-weight: 500;
  color: #495057;
}

/* 地图容器样式 */
.map-wrapper {
  position: relative;
  margin: 0;
}

.no-api-key {
  height: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #6c757d;
  text-align: center;
}

.no-api-key h3 {
  margin: 0 0 1rem;
  font-size: 1.5rem;
}

.no-api-key a {
  color: #007bff;
  text-decoration: none;
  font-weight: 500;
}

.no-api-key a:hover {
  text-decoration: underline;
}

/* 操作按钮样式 */
.actions {
  padding: 1.5rem 2rem;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.btn-action {
  padding: 0.75rem 1.5rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-action:hover {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* 状态面板样式 */
.status-panel {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
}

.status-panel h3 {
  margin: 0 0 1rem;
  color: #495057;
  font-size: 1.2rem;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.status-item label {
  font-weight: 500;
  color: #495057;
}

.status-value {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.9rem;
  color: #212529;
}

.status-value.success {
  color: #28a745;
  font-weight: 500;
}

.status-value.error {
  color: #dc3545;
  font-weight: 500;
}

/* 日志面板样式 */
.log-panel {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
}

.log-panel h3 {
  margin: 0 0 1rem;
  color: #495057;
  font-size: 1.2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  background: #f8f9fa;
  margin-bottom: 1rem;
}

.log-item {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  align-items: flex-start;
}

.log-item:last-child {
  border-bottom: none;
}

.log-item.success {
  background: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

.log-item.warning {
  background: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

.log-item.error {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

.log-item.info {
  background: #d1ecf1;
  color: #0c5460;
  border-color: #bee5eb;
}

.log-time {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 0.8rem;
  color: #6c757d;
  min-width: 80px;
  font-weight: 500;
}

.log-message {
  flex: 1;
  word-break: break-word;
}

.log-empty {
  padding: 2rem;
  text-align: center;
  color: #6c757d;
  font-style: italic;
}

.btn-clear-logs {
  padding: 0.5rem 1rem;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-clear-logs:hover {
  background: #5a6268;
}

/* 底部样式 */
.app-footer {
  text-align: center;
  padding: 2rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.app-footer p {
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-main {
    padding: 1rem;
  }
  
  .control-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-group label {
    min-width: auto;
  }
  
  .api-input {
    min-width: auto;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
}

/* 滚动条样式 */
.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style> 