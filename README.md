# Vue3 HMap

基于华为地图封装的Vue3地图组件库，提供了简单易用的Vue3组件来集成华为地图功能。

## 🚀 特性

- 🔥 **Vue3 支持**: 完全支持Vue3的Composition API
- 🛠️ **TypeScript**: 内置TypeScript类型定义
- 📱 **响应式**: 支持响应式数据绑定
- 🎨 **组件化**: 提供多种地图组件（地图、标记、信息窗口等）
- 🔧 **易于使用**: 简单的API设计，快速集成
- 📦 **按需引入**: 支持按需引入组件

## 📦 安装

```bash
# 使用 npm
npm install vue3-hmap

# 使用 pnpm
pnpm add vue3-hmap

# 使用 yarn
yarn add vue3-hmap
```

## 🔧 使用方法

### 全局注册

```typescript
import { createApp } from 'vue'
import Vue3HMap from 'vue3-hmap'

const app = createApp(App)
app.use(Vue3HMap)
```

### 按需引入

```vue
<template>
  <div style="height: 400px">
    <HWMap
      :api-key="apiKey"
      :center="center"
      :zoom="zoom"
      @ready="onMapReady"
      @click="onMapClick"
    >
      <HWMarker
        v-for="marker in markers"
        :key="marker.id"
        :position="marker.position"
        :label="marker.label"
        @click="onMarkerClick(marker)"
      />
      
      <HWInfoWindow
        v-if="infoWindow.visible"
        :position="infoWindow.position"
        :content="infoWindow.content"
        :visible="infoWindow.visible"
        @close="infoWindow.visible = false"
      />
    </HWMap>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { HWMap, HWMarker, HWInfoWindow } from 'vue3-hmap'

const apiKey = ref('YOUR_HUAWEI_MAP_API_KEY')
const center = ref({ lat: 39.9042, lng: 116.4074 })
const zoom = ref(10)
const markers = ref([])
const infoWindow = ref({
  visible: false,
  position: { lat: 0, lng: 0 },
  content: ''
})

const onMapReady = (map) => {
  console.log('地图准备完成', map)
}

const onMapClick = (event) => {
  // 添加标记
  const marker = {
    id: Date.now(),
    position: event.coordinate,
    label: '新标记'
  }
  markers.value.push(marker)
}

const onMarkerClick = (marker) => {
  infoWindow.value = {
    visible: true,
    position: marker.position,
    content: `<div><h4>${marker.label}</h4><p>位置: ${marker.position.lat}, ${marker.position.lng}</p></div>`
  }
}
</script>
```

## 📘 组件文档

### HWMap 地图组件

基础地图组件，用于显示华为地图。

#### Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| apiKey | string | 是 | - | 华为地图API Key |
| center | LatLng | 是 | - | 地图中心点坐标 |
| zoom | number | 否 | 10 | 地图缩放级别 |
| width | string | 否 | '100%' | 地图宽度 |
| height | string | 否 | '400px' | 地图高度 |
| language | string | 否 | 'zh' | 地图语言 |
| mapType | string | 否 | 'ROADMAP' | 地图类型 |
| presetStyleId | string | 否 | 'standard' | 预设样式ID |
| zoomControl | boolean | 否 | true | 是否显示缩放控件 |
| scaleControl | boolean | 否 | false | 是否显示比例尺 |
| rotateControl | boolean | 否 | false | 是否显示指北针 |
| locationControl | boolean | 否 | false | 是否显示定位控件 |
| minZoom | number | 否 | 2 | 最小缩放级别 |
| maxZoom | number | 否 | 20 | 最大缩放级别 |
| sourceType | string | 否 | 'vector' | 瓦片类型 |

#### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| ready | (map: HWMap) | 地图准备完成 |
| error | (error: string) | 地图加载错误 |
| click | (event: any) | 地图点击事件 |
| dblclick | (event: any) | 地图双击事件 |
| center-changed | (center: LatLng) | 地图中心点变化 |
| zoom-changed | (zoom: number) | 地图缩放级别变化 |

#### 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| setCenter | (center: LatLng) | void | 设置地图中心点 |
| setZoom | (zoom: number) | void | 设置地图缩放级别 |
| getCenter | () | LatLng | 获取地图中心点 |
| getZoom | () | number | 获取地图缩放级别 |
| resize | () | void | 重新调整地图大小 |

### HWMarker 标记组件

用于在地图上显示标记。

#### Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| position | LatLng | 是 | - | 标记位置 |
| icon | string \| MarkerIconOption | 否 | - | 标记图标 |
| label | string \| MarkerLabelOption | 否 | - | 标记标签 |
| draggable | boolean | 否 | false | 是否可拖拽 |
| animation | string | 否 | null | 动画效果 |
| zIndex | number | 否 | 0 | z-index |
| visible | boolean | 否 | true | 是否可见 |

#### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | (event: any) | 标记点击事件 |
| dblclick | (event: any) | 标记双击事件 |
| position-changed | (position: LatLng) | 标记位置变化 |
| drag-start | (event: any) | 拖拽开始 |
| drag-end | (event: any) | 拖拽结束 |

### HWInfoWindow 信息窗口组件

用于显示信息窗口。

#### Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| position | LatLng | 是 | - | 信息窗口位置 |
| content | string | 否 | '' | 信息窗口内容 |
| visible | boolean | 否 | false | 是否可见 |
| offset | [number, number] | 否 | - | 偏移量 |
| marker | HWMarker | 否 | - | 绑定的标记 |

#### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| open | () | 信息窗口打开 |
| close | () | 信息窗口关闭 |

## 🔧 开发

```bash
# 克隆项目
git clone https://github.com/your-username/vue3-hmap.git

# 进入项目目录
cd vue3-hmap

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建库
pnpm build:lib

# 类型检查
pnpm type-check
```

## 📝 类型定义

本库提供完整的TypeScript类型定义，包括：

- `LatLng`: 经纬度坐标
- `MapOptions`: 地图配置选项
- `MarkerOptions`: 标记配置选项
- `InfoWindowOptions`: 信息窗口配置选项
- 各种事件类型定义

## 🤝 贡献

欢迎贡献代码！请阅读[贡献指南](CONTRIBUTING.md)了解详细信息。

## 📄 许可证

[MIT](LICENSE)

## 🔗 相关链接

- [华为地图官方文档](https://developer.huawei.com/consumer/cn/doc/development/HMS-Guides/Maps-SDK-for-JavaScript-Dev-Guide-V5)
- [Vue3 官方文档](https://cn.vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)

## 💡 常见问题

### 如何获取华为地图API Key？

1. 访问[华为开发者联盟](https://developer.huawei.com/)
2. 注册开发者账号
3. 创建应用并开通地图服务
4. 获取API Key

### 组件不显示怎么办？

1. 检查API Key是否正确
2. 确认网络连接正常
3. 查看控制台是否有错误信息
4. 确认地图容器有正确的宽高

## 📈 更新日志

### v0.1.0

- 🎉 首次发布
- ✨ 支持基础地图显示
- ✨ 支持标记和信息窗口
- ✨ 完整的TypeScript类型定义

---

如有问题或建议，请提交[Issue](https://github.com/your-username/vue3-hmap/issues)。



