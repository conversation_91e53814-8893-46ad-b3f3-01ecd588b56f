<template>
  <div style="display: none;">
    <slot :marker="markerInstance" />
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, onUnmounted, watch } from 'vue'
import type { HWMap, HWMarker, MarkerOptions, MarkerIconOption, MarkerLabelOption } from '@/types'
import { getHWMapSDK, isValidLatLng } from '@/utils'

interface Props {
  /** 标记位置 */
  position: { lat: number; lng: number }
  /** 标记图标 */
  icon?: string | MarkerIconOption
  /** 标记标签 */
  label?: string | MarkerLabelOption
  /** 是否可拖拽 */
  draggable?: boolean
  /** 动画效果 */
  animation?: 'DROP' | 'BOUNCE' | null
  /** z-index */
  zIndex?: number
  /** 自定义属性 */
  properties?: Record<string, any>
  /** 是否可见 */
  visible?: boolean
}

interface Emits {
  (event: 'click', clickEvent: any): void
  (event: 'dblclick', dblclickEvent: any): void
  (event: 'contextmenu', contextmenuEvent: any): void
  (event: 'mousedown', mousedownEvent: any): void
  (event: 'mouseup', mouseupEvent: any): void
  (event: 'mouseover', mouseoverEvent: any): void
  (event: 'mouseout', mouseoutEvent: any): void
  (event: 'position-changed', position: { lat: number; lng: number }): void
  (event: 'drag-start', dragEvent: any): void
  (event: 'drag', dragEvent: any): void
  (event: 'drag-end', dragEvent: any): void
}

const props = withDefaults(defineProps<Props>(), {
  draggable: false,
  animation: null,
  zIndex: 0,
  visible: true
})

const emit = defineEmits<Emits>()

// 注入地图实例
const mapInstance = inject<{ value: HWMap | null }>('hwMap')

// 响应式状态
const markerInstance = ref<HWMarker | null>(null)

// 事件监听器
const eventListeners = new Map<string, Function>()

/**
 * 创建标记实例
 */
async function createMarker(): Promise<void> {
  if (!mapInstance?.value) {
    console.warn('HWMarker: 地图实例未找到')
    return
  }

  if (!isValidLatLng(props.position)) {
    console.warn('HWMarker: 无效的位置坐标')
    return
  }

  const SDK = getHWMapSDK()
  if (!SDK) {
    console.warn('HWMarker: 华为地图 SDK 未加载')
    return
  }

  try {
    // 创建标记选项
    const markerOptions: MarkerOptions = {
      map: mapInstance.value,
      position: props.position,
      icon: props.icon,
      label: props.label,
      draggable: props.draggable,
      animation: props.animation,
      zIndex: props.zIndex,
      properties: props.properties
    }

    // 创建标记实例
    markerInstance.value = new SDK.HWMarker(markerOptions)

    // 设置可见性
    if (!props.visible) {
      markerInstance.value.setMap(null)
    }

    // 绑定事件监听器
    bindEventListeners()

  } catch (error) {
    console.error('HWMarker: 创建标记失败', error)
  }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners(): void {
  if (!markerInstance.value) return

  const marker = markerInstance.value

  // 点击事件
  const clickHandler = (event: any) => emit('click', event)
  marker.addListener('click', clickHandler)
  eventListeners.set('click', clickHandler)

  // 双击事件
  const dblclickHandler = (event: any) => emit('dblclick', event)
  marker.addListener('dblclick', dblclickHandler)
  eventListeners.set('dblclick', dblclickHandler)

  // 右键菜单事件
  const contextmenuHandler = (event: any) => emit('contextmenu', event)
  marker.addListener('contextmenu', contextmenuHandler)
  eventListeners.set('contextmenu', contextmenuHandler)

  // 鼠标按下事件
  const mousedownHandler = (event: any) => emit('mousedown', event)
  marker.addListener('mousedown', mousedownHandler)
  eventListeners.set('mousedown', mousedownHandler)

  // 鼠标松开事件
  const mouseupHandler = (event: any) => emit('mouseup', event)
  marker.addListener('mouseup', mouseupHandler)
  eventListeners.set('mouseup', mouseupHandler)

  // 鼠标移入事件
  const mouseoverHandler = (event: any) => emit('mouseover', event)
  marker.addListener('mouseOver', mouseoverHandler)
  eventListeners.set('mouseOver', mouseoverHandler)

  // 鼠标移出事件
  const mouseoutHandler = (event: any) => emit('mouseout', event)
  marker.addListener('mouseOut', mouseoutHandler)
  eventListeners.set('mouseOut', mouseoutHandler)

  // 位置变化事件
  const positionChangedHandler = (position: { lat: number; lng: number }) => {
    emit('position-changed', position)
  }
  marker.addListener('position_changed', positionChangedHandler)
  eventListeners.set('position_changed', positionChangedHandler)

  // 拖拽事件（如果启用拖拽）
  if (props.draggable) {
    const dragStartHandler = (event: any) => emit('drag-start', event)
    marker.addListener('dragstart', dragStartHandler)
    eventListeners.set('dragstart', dragStartHandler)

    const dragHandler = (event: any) => emit('drag', event)
    marker.addListener('drag', dragHandler)
    eventListeners.set('drag', dragHandler)

    const dragEndHandler = (event: any) => emit('drag-end', event)
    marker.addListener('dragend', dragEndHandler)
    eventListeners.set('dragend', dragEndHandler)
  }
}

/**
 * 解绑事件监听器
 */
function unbindEventListeners(): void {
  if (!markerInstance.value) return

  const marker = markerInstance.value
  eventListeners.forEach((handler, event) => {
    marker.removeListener(event, handler)
  })
  eventListeners.clear()
}

/**
 * 销毁标记
 */
function destroyMarker(): void {
  if (markerInstance.value) {
    unbindEventListeners()
    markerInstance.value.setMap(null)
    markerInstance.value = null
  }
}

/**
 * 设置标记位置
 */
function setPosition(position: { lat: number; lng: number }): void {
  if (markerInstance.value && isValidLatLng(position)) {
    markerInstance.value.setPosition(position)
  }
}

/**
 * 获取标记位置
 */
function getPosition(): { lat: number; lng: number } | null {
  return markerInstance.value?.getPosition() || null
}

/**
 * 设置标记图标
 */
function setIcon(icon: string | MarkerIconOption): void {
  if (markerInstance.value) {
    markerInstance.value.setIcon(icon)
  }
}

/**
 * 设置标记标签
 */
function setLabel(label: string | MarkerLabelOption): void {
  if (markerInstance.value) {
    markerInstance.value.setLabel(label)
  }
}

/**
 * 设置是否可拖拽
 */
function setDraggable(draggable: boolean): void {
  if (markerInstance.value) {
    markerInstance.value.setDraggable(draggable)
  }
}

/**
 * 设置动画
 */
function setAnimation(animation: 'DROP' | 'BOUNCE' | null): void {
  if (markerInstance.value) {
    markerInstance.value.setAnimation(animation)
  }
}

/**
 * 设置 z-index
 */
function setZIndex(zIndex: number): void {
  if (markerInstance.value) {
    markerInstance.value.setZIndex(zIndex)
  }
}

/**
 * 设置可见性
 */
function setVisible(visible: boolean): void {
  if (markerInstance.value) {
    if (visible) {
      markerInstance.value.setMap(mapInstance?.value || null)
    } else {
      markerInstance.value.setMap(null)
    }
  }
}

// 监听属性变化
watch(
  () => props.position,
  (newPosition: { lat: number; lng: number }) => {
    if (markerInstance.value && isValidLatLng(newPosition)) {
      setPosition(newPosition)
    }
  },
  { deep: true }
)

watch(
  () => props.icon,
  (newIcon: string | MarkerIconOption | undefined) => {
    if (markerInstance.value && newIcon !== undefined) {
      setIcon(newIcon)
    }
  }
)

watch(
  () => props.label,
  (newLabel: string | MarkerLabelOption | undefined) => {
    if (markerInstance.value && newLabel !== undefined) {
      setLabel(newLabel)
    }
  }
)

watch(
  () => props.draggable,
  (newDraggable: boolean) => {
    if (markerInstance.value) {
      setDraggable(newDraggable)
    }
  }
)

watch(
  () => props.animation,
  (newAnimation: 'DROP' | 'BOUNCE' | null) => {
    if (markerInstance.value) {
      setAnimation(newAnimation)
    }
  }
)

watch(
  () => props.zIndex,
  (newZIndex: number) => {
    if (markerInstance.value) {
      setZIndex(newZIndex)
    }
  }
)

watch(
  () => props.visible,
  (newVisible: boolean) => {
    setVisible(newVisible)
  }
)

// 监听地图实例变化
watch(
  () => mapInstance?.value,
  (newMap: HWMap | null) => {
    if (newMap && !markerInstance.value) {
      createMarker()
    } else if (!newMap && markerInstance.value) {
      destroyMarker()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  marker: markerInstance,
  setPosition,
  getPosition,
  setIcon,
  setLabel,
  setDraggable,
  setAnimation,
  setZIndex,
  setVisible
})

// 生命周期
onMounted(() => {
  if (mapInstance?.value) {
    createMarker()
  }
})

onUnmounted(() => {
  destroyMarker()
})
</script>

<style scoped>
/* 标记组件本身不需要样式 */
</style> 