<template>
  <div style="display: none;">
    <slot :info-window="infoWindowInstance" />
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, onUnmounted, watch } from 'vue'
import type { HWMap, HWInfoWindow, HWMarker, InfoWindowOptions } from '@/types'
import { getHWMapSDK, isValidLatLng } from '@/utils'

interface Props {
  /** 信息窗口位置 */
  position: { lat: number; lng: number }
  /** 信息窗口内容 */
  content?: string
  /** 偏移量 */
  offset?: [number, number]
  /** 是否可见 */
  visible?: boolean
  /** 绑定的标记 */
  marker?: HWMarker
}

interface Emits {
  (event: 'close'): void
  (event: 'open'): void
}

const props = withDefaults(defineProps<Props>(), {
  content: '',
  visible: false
})

const emit = defineEmits<Emits>()

// 注入地图实例
const mapInstance = inject<{ value: HWMap | null }>('hwMap')

// 响应式状态
const infoWindowInstance = ref<HWInfoWindow | null>(null)

// 事件监听器
const eventListeners = new Map<string, Function>()

/**
 * 创建信息窗口实例
 */
async function createInfoWindow(): Promise<void> {
  if (!mapInstance?.value) {
    console.warn('HWInfoWindow: 地图实例未找到')
    return
  }

  if (!isValidLatLng(props.position)) {
    console.warn('HWInfoWindow: 无效的位置坐标')
    return
  }

  const SDK = getHWMapSDK()
  if (!SDK) {
    console.warn('HWInfoWindow: 华为地图 SDK 未加载')
    return
  }

  try {
    // 创建信息窗口选项
    const infoWindowOptions: InfoWindowOptions = {
      map: mapInstance.value,
      position: props.position,
      content: props.content,
      offset: props.offset
    }

    // 创建信息窗口实例
    infoWindowInstance.value = new SDK.HWInfoWindow(infoWindowOptions)

    // 绑定事件监听器
    bindEventListeners()

    // 如果初始时可见，则显示信息窗口
    if (props.visible) {
      showInfoWindow()
    }

  } catch (error) {
    console.error('HWInfoWindow: 创建信息窗口失败', error)
  }
}

/**
 * 绑定事件监听器
 */
function bindEventListeners(): void {
  if (!infoWindowInstance.value) return

  const infoWindow = infoWindowInstance.value

  // 关闭事件
  const closeHandler = () => {
    emit('close')
  }
  infoWindow.addListener('close', closeHandler)
  eventListeners.set('close', closeHandler)
}

/**
 * 解绑事件监听器
 */
function unbindEventListeners(): void {
  if (!infoWindowInstance.value) return

  const infoWindow = infoWindowInstance.value
  eventListeners.forEach((handler, event) => {
    infoWindow.removeListener(event, handler)
  })
  eventListeners.clear()
}

/**
 * 显示信息窗口
 */
function showInfoWindow(): void {
  if (!infoWindowInstance.value) return

  if (props.marker) {
    // 如果有绑定的标记，则在标记上打开信息窗口
    infoWindowInstance.value.open(props.marker)
  } else {
    // 否则在指定位置打开信息窗口
    infoWindowInstance.value.setPosition(props.position)
  }
  emit('open')
}

/**
 * 隐藏信息窗口
 */
function hideInfoWindow(): void {
  if (infoWindowInstance.value) {
    infoWindowInstance.value.close()
  }
}

/**
 * 销毁信息窗口
 */
function destroyInfoWindow(): void {
  if (infoWindowInstance.value) {
    unbindEventListeners()
    infoWindowInstance.value.close()
    infoWindowInstance.value = null
  }
}

/**
 * 设置信息窗口位置
 */
function setPosition(position: { lat: number; lng: number }): void {
  if (infoWindowInstance.value && isValidLatLng(position)) {
    infoWindowInstance.value.setPosition(position)
  }
}

/**
 * 获取信息窗口位置
 */
function getPosition(): { lat: number; lng: number } | null {
  return infoWindowInstance.value?.getPosition() || null
}

/**
 * 设置信息窗口内容
 */
function setContent(content: string | HTMLElement): void {
  if (infoWindowInstance.value) {
    infoWindowInstance.value.setContent(content)
  }
}

/**
 * 获取信息窗口内容
 */
function getContent(): string | HTMLElement | null {
  return infoWindowInstance.value?.getContent() || null
}

/**
 * 打开信息窗口
 */
function open(marker?: HWMarker): void {
  if (infoWindowInstance.value) {
    if (marker) {
      infoWindowInstance.value.open(marker)
    } else {
      showInfoWindow()
    }
  }
}

/**
 * 关闭信息窗口
 */
function close(): void {
  hideInfoWindow()
}

// 监听属性变化
watch(
  () => props.position,
  (newPosition: { lat: number; lng: number }) => {
    if (infoWindowInstance.value && isValidLatLng(newPosition)) {
      setPosition(newPosition)
    }
  },
  { deep: true }
)

watch(
  () => props.content,
  (newContent: string | undefined) => {
    if (infoWindowInstance.value && newContent !== undefined) {
      setContent(newContent)
    }
  }
)

watch(
  () => props.visible,
  (newVisible: boolean) => {
    if (infoWindowInstance.value) {
      if (newVisible) {
        showInfoWindow()
      } else {
        hideInfoWindow()
      }
    }
  }
)

// 监听地图实例变化
watch(
  () => mapInstance?.value,
  (newMap: HWMap | null) => {
    if (newMap && !infoWindowInstance.value) {
      createInfoWindow()
    } else if (!newMap && infoWindowInstance.value) {
      destroyInfoWindow()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  infoWindow: infoWindowInstance,
  setPosition,
  getPosition,
  setContent,
  getContent,
  open,
  close,
  show: showInfoWindow,
  hide: hideInfoWindow
})

// 生命周期
onMounted(() => {
  if (mapInstance?.value) {
    createInfoWindow()
  }
})

onUnmounted(() => {
  destroyInfoWindow()
})
</script>

<style scoped>
/* 信息窗口组件本身不需要样式 */
</style> 