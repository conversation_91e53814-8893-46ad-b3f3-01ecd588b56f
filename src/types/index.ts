// 华为地图 API 类型定义
export interface HWMapJsSDK {
  HWMap: new (div: HTMLElement, options: MapOptions) => HWMap
  HWMarker: new (options: MarkerOptions) => HWMarker
  HWInfoWindow: new (options: InfoWindowOptions) => HWInfoWindow
  HWCustomPoi: new (options: CustomPoiOptions) => HWCustomPoi
  HWAutocomplete: new (input: HTMLInputElement, options: AutocompleteOptions) => HWAutocomplete
  HWSiteService: new () => HWSiteService
  HWDirectionsService: new () => HWDirectionsService
  HWDirectionsRenderer: new () => HWDirectionsRenderer
  HWMarkerCluster: new (map: HWMap, markers: HWMarker[], options: MarkerClusterOptions) => HWMarkerCluster
}

export interface LatLng {
  lat: number
  lng: number
}

export interface AuthOptions {
  accessToken: string
}

export interface MapOptions {
  center: LatLng
  zoom: number
  authOptions?: AuthOptions
  copyrightControl?: boolean
  language?: string
  locationControl?: boolean
  logoPosition?: 'BOTTOM_LEFT' | 'BOTTOM_RIGHT' | 'TOP_LEFT' | 'TOP_RIGHT'
  mapType?: 'ROADMAP' | 'TERRAIN'
  maxZoom?: number
  minZoom?: number
  navigationControl?: boolean
  presetStyleId?: 'standard' | 'night' | 'simple'
  rotateControl?: boolean
  scaleControl?: boolean
  sourceType?: 'vector' | 'raster'
  zoomSlider?: boolean
  zoomControl?: boolean
  rasterPreload?: boolean
}

export interface HWMap {
  fitBounds(bounds: LatLngBounds): void
  fromScreenLocation(pixel: { x: number; y: number }): LatLng
  getBounds(): LatLngBounds
  getCenter(): LatLng
  getDiv(): HTMLElement
  getHeading(): number
  getMapType(): string
  getOpacity(): number
  getSize(): [number, number]
  getZoom(): number
  panBy(x: number, y: number): void
  panTo(latLng: LatLng): void
  resize(): void
  setCenter(latLng: LatLng): void
  setZoom(zoom: number): void
  on(event: string, callback: (event: any) => void): void
  onCenterChanged(callback: (center: LatLng) => void): void
  onZoomChanged(callback: (zoom: number) => void): void
  onHeadingChanged(callback: (heading: number) => void): void
  un(event: string, callback: (event: any) => void): void
}

export interface LatLngBounds {
  ne: LatLng
  sw: LatLng
}

export interface MarkerOptions {
  map: HWMap
  position: LatLng
  icon?: string | MarkerIconOption
  label?: string | MarkerLabelOption
  draggable?: boolean
  animation?: 'DROP' | 'BOUNCE' | null
  zIndex?: number
  properties?: Record<string, any>
}

export interface MarkerIconOption {
  url?: string
  anchor?: [number, number]
  anchorUnit?: 'fraction' | 'pixels'
  opacity?: number
  rotation?: number
  scale?: number
  quantityUrls?: QuantityUrls[]
}

export interface MarkerLabelOption {
  text: string
  color?: string
  fontSize?: string
  fontFamily?: string
  offsetX?: number
  offsetY?: number
  strokeColor?: string
  strokeWeight?: number
}

export interface QuantityUrls {
  num: number
  url: string
}

export interface HWMarker {
  getAnimation(): string
  getDraggable(): boolean
  getIcon(): string | MarkerIconOption
  getLabel(): string | MarkerLabelOption
  getMap(): HWMap
  getPosition(): LatLng
  getZIndex(): number
  setAnimation(animation: 'DROP' | 'BOUNCE' | null): void
  setDraggable(draggable: boolean): void
  setIcon(icon: string | MarkerIconOption): void
  setLabel(label: string | MarkerLabelOption): void
  setMap(map: HWMap): void
  setPosition(position: LatLng): void
  setZIndex(zIndex: number): void
  addListener(event: string, callback: Function): void
  removeListener(event: string, callback: Function): void
}

export interface InfoWindowOptions {
  map: HWMap
  position: LatLng
  content?: string | HTMLElement
  offset?: [number, number]
}

export interface HWInfoWindow {
  close(): void
  getContent(): string | HTMLElement
  getPosition(): LatLng
  open(marker: HWMarker): void
  setContent(content: string | HTMLElement): void
  setPosition(position: LatLng): void
  addListener(event: string, callback: Function): void
  removeListener(event: string, callback: Function): void
}

export interface CustomPoiOptions {
  map: HWMap
  position: LatLng
  icon?: string | CustomPoiIconOption
  label?: string | CustomPoiLabelOption
  collisionEnabled?: boolean
  variableLabelAnchor?: string[]
  zIndex?: number
}

export interface CustomPoiIconOption {
  url?: string
  anchor?: string
  opacity?: number
  rotation?: number
  scale?: number
}

export interface CustomPoiLabelOption {
  text: string
  color?: string
  fontSize?: string
  offsetX?: number
  offsetY?: number
  strokeColor?: string
  strokeWeight?: number
}

export interface HWCustomPoi {
  getCollisionEnabled(): boolean
  getIcon(): string | CustomPoiIconOption
  getLabel(): string | CustomPoiLabelOption
  getMap(): HWMap
  getPosition(): LatLng
  getZIndex(): number
  setCollisionEnabled(enabled: boolean): void
  setIcon(icon: string | CustomPoiIconOption): void
  setLabel(label: string | CustomPoiLabelOption): void
  setMap(map: HWMap): void
  setPosition(position: LatLng): void
  setZIndex(zIndex: number): void
  addListener(event: string, callback: Function): void
  removeListener(event: string, callback: Function): void
}

export interface AutocompleteOptions {
  bounds?: LatLngBounds
  countries?: string[]
  countryCode?: string
  customHandler?: Function
  data?: any
  language?: string
  location?: LatLng
  maxHeight?: string
  poiType?: string
  radius?: number
}

export interface HWAutocomplete {
  addListener(eventName: string, callback: Function): void
  getSite(): Site
}

export interface Site {
  siteId: string
  name?: string
  formatAddress: string
  location?: LatLng
  distance?: number
}

export interface HWSiteService {
  geocode(request: GeocodeRequest, callback: Function): void
  nearbySearch(request: NearbySearchRequest, callback: Function): void
  querySuggestion(request: QuerySuggestionRequest, callback: Function): void
  reverseGeocode(request: ReverseGeocodeRequest, callback: Function): void
  searchById(request: SearchByIdRequest, callback: Function): void
  searchByText(request: SearchByTextRequest, callback: Function): void
}

export interface GeocodeRequest {
  address: string
  bounds?: LatLngBounds
  language?: string
}

export interface NearbySearchRequest {
  location: LatLng
  radius?: number
  query?: string
  pageIndex?: number
  pageSize?: number
  language?: string
}

export interface QuerySuggestionRequest {
  query: string
  location?: LatLng
  radius?: number
  bounds?: LatLngBounds
  language?: string
}

export interface ReverseGeocodeRequest {
  location: LatLng
  language?: string
  returnPoi?: boolean
}

export interface SearchByIdRequest {
  siteId: string
  language?: string
}

export interface SearchByTextRequest {
  query: string
  location?: LatLng
  radius?: number
  pageIndex?: number
  pageSize?: number
  language?: string
}

export interface HWDirectionsService {
  routeWalking(request: DirectionsRequest, callback: Function): void
  routeBicycling(request: DirectionsRequest, callback: Function): void
  routeDriving(request: DrivingDirectionsRequest, callback: Function): void
}

export interface DirectionsRequest {
  origin: LatLng
  destination: LatLng
}

export interface DrivingDirectionsRequest extends DirectionsRequest {
  waypoints?: LatLng[]
  viaType?: boolean
  optimize?: boolean
  alternatives?: boolean
  avoid?: number[]
  departAt?: number
  trafficMode?: number
}

export interface HWDirectionsRenderer {
  setMap(map: HWMap): void
  getMap(): HWMap
  setDirections(directionsResult: any, options?: any): void
}

export interface MarkerClusterOptions {
  icon?: string | MarkerIconOption
  label?: string | MarkerLabelOption
  renderClusterMarker?: Function
  zIndex?: number
}

export interface HWMarkerCluster {
  addMarker(marker: HWMarker): void
  addMarkers(markers: HWMarker[]): void
  clearMarkers(): void
  getMarkers(): HWMarker[]
  removeMarker(marker: HWMarker): void
  removeMarkers(markers: HWMarker[]): void
  getZIndex(): number
  setZIndex(zIndex: number): void
  addListener(event: string, callback: Function): void
  removeListener(event: string, callback: Function): void
}

// 全局声明
declare global {
  interface Window {
    HWMapJsSDK: HWMapJsSDK
  }
} 