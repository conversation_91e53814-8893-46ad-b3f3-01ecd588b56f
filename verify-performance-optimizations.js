#!/usr/bin/env node

/**
 * 性能优化验证脚本
 * 检查所有性能优化措施是否正确实施
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 开始验证性能优化措施...\n');

const optimizations = [
    {
        name: 'Console日志优化',
        file: 'src/components/HWMap.vue',
        checks: [
            { pattern: /const isDev = import\.meta\.env\.DEV/, description: '开发环境判断' },
            { pattern: /const devLog = isDev \? console\.log : \(\) => \{\}/, description: '条件日志函数' },
            { pattern: /devLog\(/g, description: '使用devLog替代console.log', count: true }
        ]
    },
    {
        name: '事件防抖/节流优化',
        file: 'src/components/HWMap.vue',
        checks: [
            { pattern: /import.*debounce.*throttle/, description: '导入防抖/节流函数' },
            { pattern: /debounce\(\(event: any\) => \{/, description: '中心点变化防抖' },
            { pattern: /throttle\(\(event: any\) => \{/, description: '缩放变化节流' },
            { pattern: /\}, 300\).*防抖/, description: '300ms防抖设置' },
            { pattern: /\}, 100\).*节流/, description: '100ms节流设置' }
        ]
    },
    {
        name: '定时器优化',
        file: 'src/App.vue',
        checks: [
            { pattern: /\/\/ setInterval\(validateAndResetConfig, 30000\)/, description: '定时器已注释' },
            { pattern: /移除定期检查定时器以提升性能/, description: '添加了优化说明' }
        ]
    },
    {
        name: '日志系统优化',
        file: 'src/App.vue',
        checks: [
            { pattern: /logs\.value\.length > 50/, description: '日志数量限制为50' },
            { pattern: /减少日志数量以提升性能/, description: '添加了优化注释' }
        ]
    }
];

let totalChecks = 0;
let passedChecks = 0;
let totalConsoleLogReplacements = 0;

optimizations.forEach((optimization, index) => {
    console.log(`${index + 1}. 检查优化: ${optimization.name}`);
    
    const filePath = path.join(__dirname, optimization.file);
    
    if (!fs.existsSync(filePath)) {
        console.log(`   ❌ 文件不存在: ${optimization.file}`);
        return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    optimization.checks.forEach(check => {
        totalChecks++;
        
        if (check.count) {
            // 计数检查
            const matches = content.match(check.pattern);
            const count = matches ? matches.length : 0;
            if (count > 0) {
                console.log(`   ✅ ${check.description}: ${count}处`);
                passedChecks++;
                if (check.description.includes('devLog')) {
                    totalConsoleLogReplacements = count;
                }
            } else {
                console.log(`   ❌ ${check.description}: 未找到`);
            }
        } else {
            // 普通检查
            if (check.pattern.test(content)) {
                console.log(`   ✅ ${check.description}`);
                passedChecks++;
            } else {
                console.log(`   ❌ ${check.description}`);
            }
        }
    });
    
    console.log('');
});

// 检查是否还有未优化的console.log
console.log('🔍 检查是否还有未优化的console.log...');
const filesToCheck = [
    'src/components/HWMap.vue',
    'src/components/HWMarker.vue',
    'src/components/HWInfoWindow.vue'
];

let remainingConsoleLogs = 0;
filesToCheck.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        // 查找未优化的console.log (不包括注释中的)
        const matches = content.match(/^\s*console\.log\(/gm);
        if (matches) {
            remainingConsoleLogs += matches.length;
            console.log(`   ⚠️  ${file}: 发现 ${matches.length} 个未优化的console.log`);
        }
    }
});

if (remainingConsoleLogs === 0) {
    console.log('   ✅ 没有发现未优化的console.log');
    passedChecks++;
    totalChecks++;
} else {
    console.log(`   ❌ 总共发现 ${remainingConsoleLogs} 个未优化的console.log`);
    totalChecks++;
}

// 性能影响评估
console.log('\n📊 性能优化影响评估:');
console.log(`   Console日志优化: ${totalConsoleLogReplacements} 处日志已优化`);
console.log(`   事件优化: 添加了防抖和节流机制`);
console.log(`   定时器优化: 移除了不必要的30秒定时器`);
console.log(`   日志系统优化: 日志数量从100减少到50`);

console.log('\n📈 预期性能提升:');
console.log('   LCP: 从 8.51s 预期降至 < 2.5s (70%+ 改善)');
console.log('   INP: 从 3,464ms 预期降至 < 200ms (95%+ 改善)');
console.log('   内存使用: 减少日志和定时器开销');

console.log('\n📋 验证结果总结:');
console.log(`   总检查项: ${totalChecks}`);
console.log(`   通过检查: ${passedChecks}`);
console.log(`   成功率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

if (passedChecks === totalChecks) {
    console.log('\n🎉 所有性能优化措施验证通过！');
    console.log('💡 建议使用Chrome DevTools的Performance面板测试实际性能改善效果。');
    process.exit(0);
} else {
    console.log('\n⚠️  部分优化措施可能存在问题，请检查上述失败项');
    process.exit(1);
}
